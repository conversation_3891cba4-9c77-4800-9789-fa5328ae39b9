apiVersion: opensearch.opster.io/v1
kind: OpensearchIndexTemplate
metadata:
  name: traces-template
spec:
  opensearchCluster:
    name: opensearch-otel

  name: traces_data_type 

  indexPatterns: 
    - "otel-v1-apm-span-*"
  priority: 101

  template:
    settings:
      index.routing.allocation.require.temp: "hot"
    mappings:
      properties:
        span:
          type: "object"
          properties: 
            attributes: 
              type: "object"
              properties: 
                cpuTimeInNanos:
                  type: "long"
                allocatedBytes:
                  type: "long"