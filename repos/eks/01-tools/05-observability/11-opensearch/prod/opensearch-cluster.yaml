apiVersion: opensearch.opster.io/v1
kind: OpenSearchCluster
metadata:
  name: opensearch-otel
  namespace: monitoring-prod
spec:
  security:
    config:
      securityConfigSecret:
        name: securityconfig-secret
      adminCredentialsSecret:
        name: admin-credentials-secret
    tls:
      http:
        generate: true
      transport:
        generate: true
        perNode: true
  general:
    monitoring:
      enable: true # Enable or disable the monitoring plugin
      labels: # The labels add for ServiceMonitor
        app: opensearch
      scrapeInterval: 30s # The scrape interval for Prometheus
      monitoringUserSecret: opensearch-prometheus
      pluginUrl: https://github.com/Virtimo/prometheus-exporter-plugin-for-opensearch/releases/download/v2.19.1/prometheus-exporter-********.zip #https://github.com/Virtimo/prometheus-exporter-plugin-for-opensearch/releases
      tlsConfig: # Optional, use this to override the tlsConfig of the generated ServiceMonitor, only the following provided options can be set currently
        insecureSkipVerify: true
    setVMMaxMapCount: true
    httpPort: 9200
    serviceName: opensearch-otel
    # https://opensearch.org/releases.html
    version: 2.19.1
    pluginsList: ["repository-s3"]
    drainDataNodes: false
    snapshotRepositories:
        - name: paytrack-opensearch-operator
          type: s3
          settings:
            bucket: paytrack-opensearch-operator
            region: us-east-1
            base_path: snapshots
    additionalConfig:
      cluster.routing.allocation.node_concurrent_recoveries: "5"
      cluster.routing.allocation.cluster_concurrent_rebalance: "2"
      cluster.routing.allocation.enable: "all"
      gateway.recover_after_nodes: "2"
      gateway.expected_nodes: "3"
      indices.memory.index_buffer_size: 20%
      cluster.default.index.refresh_interval: 30s
      indices.cache.cleanup_interval: 5m
      search.default_search_timeout: 30s
      cluster.routing.allocation.disk.watermark.low: "85%"
      cluster.routing.allocation.disk.watermark.high: "90%"
      cluster.routing.allocation.disk.watermark.flood_stage: "95%"
      #sugestões de tunning - https://docs.opensearch.org/docs/latest/tuning-your-cluster/performance/
      #cluster.routing.allocation.balance.prefer_primary: "True"
      #cluster.default.index.translog.flush_threshold_size: "3072MB" #25% da heap?
      #cluster.default.index.replication.type: "SEGMENT"
      #cluster.default.index.number_of_shards: "3"
      #cluster.default.index.number_of_replicas: "1"
  dashboards:
    annotations:
      fluentbit.io/exclude: "true"
    additionalConfig:
      opensearch_security.openid.connect_url: "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_WWZcsbUlD/.well-known/openid-configuration"
      opensearch_security.openid.client_id: "4adsk82t3kbn1pj39r94cdcnev"
      opensearch_security.openid.client_secret: "1dbtbp150m0t98de5ok6oqk847snp491skfil7asbqbhg78ab5g1"
      opensearch_security.openid.base_redirect_url: "https://otel.paytrack.com.br"
      opensearch_security.openid.logout_url: "https://otel.paytrack.com.br"
      opensearch_security.openid.scope: "openid email profile"
      opensearch_security.openid.verify_hostnames: "false"
      opensearch_security.openid.header: "Authorization"
      opensearch_security.auth.type: '["openid"]'
      opensearch_security.auth.multiple_auth_enabled: "false"
      opensearch.ssl.verificationMode: none
      opensearch_security.cookie.ttl: "86400000"
      opensearch_security.session.ttl: "86400000"
      opensearch_security.session.keepalive: "true"
    tls:
      enable: false
    version: 2.19.1
    enable: true
    replicas: 2
    env:
      - name: OPENSEARCH_JAVA_OPTS
        value: "-XX:+UseContainerSupport -XX:MinRAMPercentage=50 -XX:MaxRAMPercentage=85"
    nodeSelector:
      node-role: opensearch
      kubernetes.io/arch: arm64
    tolerations:
      - key: "general-prod"
        operator: "Equal"
        value: "opensearch"
        effect: "NoSchedule"
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "2Gi"
        cpu: "1500m"
  nodePools:
    - component: masters
      #diskSize: "5Gi"
      roles:
        - "cluster_manager"
      annotations:
        fluentbit.io/exclude: "true"
      replicas: 4
      nodeSelector:
        node-role: opensearch
        kubernetes.io/arch: arm64
      tolerations:
        - key: "masters-prod"
          operator: "Equal"
          value: "opensearch"
          effect: "NoSchedule"
      env:
        - name: OPENSEARCH_JAVA_OPTS
          value: "-XX:+UseContainerSupport -XX:MinRAMPercentage=50 -XX:MaxRAMPercentage=85"
      pdb:
        enable: true
        maxUnavailable: 1
      resources:
        requests:
          memory: "3Gi"
          cpu: "1000m"
        limits:
          memory: "3Gi"
          cpu: "1500m"
      persistence:
        pvc:
          accessModes:
            - ReadWriteOnce
          storageClass: gp3-non-persistent
    - component: data-nodes
      replicas: 4
      jvm: -Xmx14000M -Xms14000M #recomendado 50% da memória total nos requests
      diskSize: "2048Gi"
      nodeSelector:
        node-role: opensearch
        kubernetes.io/arch: arm64
      tolerations:
        - key: "datanode-prod"
          operator: Equal
          value: "opensearch"
          effect: "NoSchedule"
      pdb:
        enable: true
        minAvailable: 3
      resources:
        requests:
          memory: "28Gi"
          cpu: "7000m"
        limits:
          memory: "28Gi"
          cpu: "7000m"
      roles:
        - "data"
      additionalConfig:
        node.attr.temp: "hot"
      persistence:
        pvc:
          accessModes:
            - ReadWriteOnce
          storageClass: gp3-non-persistent
    - component: cold-nodes
      replicas: 2
      jvm: -Xmx8192M -Xmx8192M #recomendado 50% da memória total nos requests
      diskSize: "6Ti"
      nodeSelector:
        node-role: opensearch
        kubernetes.io/arch: arm64
      tolerations:
        - key: cold-datanode-prod
          operator: Equal
          value: opensearch
          effect: NoSchedule
      pdb:
        enable: true
        minAvailable: 1
      resources:
        requests:
          memory: "12Gi"
          cpu: 2
        limits:
          memory: "12Gi"
          cpu: 3
      roles:
        - "data"
      additionalConfig:
        node.attr.temp: "cold"
      persistence:
        pvc:
          accessModes:
            - ReadWriteOnce
          storageClass: sc1
