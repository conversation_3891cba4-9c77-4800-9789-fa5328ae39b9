---
apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig
metadata:
  name: tools
  region: us-east-1

managedNodeGroups:
  - name: opensearch-prod-cold-datanodes
    nodeRepairConfig:
      enabled: true
    instanceTypes:
      - m8g.xlarge #4x16
    taints:
      - key: cold-datanode-prod
        value: opensearch
        effect: NoSchedule
    labels:
      node-role: opensearch
    spot: true
    minSize: 2
    maxSize: 4
    desiredCapacity: 2
    volumeSize: 50
    volumeType: gp3
    propagateASGTags: true
    iam:
      withAddonPolicies:
        autoScaler: true
        ebs: true
        efs: false
        cloudWatch: true
        xRay: true

    ssh:
      allow: false

    privateNetworking: true

    subnets: 
      #us-east-1a
      - subnet-053111dd004a4e84d
      - subnet-092662d7a92543e70
